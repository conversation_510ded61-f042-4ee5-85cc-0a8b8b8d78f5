"use client";

import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Markdown } from "@/components/ui/markdown";
import { App } from "@/core.constants";
import { SectionHeader } from "./section-header";
import { Activity, AlertTriangle, ExternalLink } from "lucide-react";

interface EmergencyActionsSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const EmergencyActionsSection = ({
  app,
  variant = "drawer",
}: EmergencyActionsSectionProps) => {
  if (app.emergency.links.length === 0) return null;

  const isModal = variant === "modal";

  return (
    <div>
      <SectionHeader
        icon={AlertTriangle}
        title="Emergency Actions"
        iconColorClass={isModal ? "bg-gradient-to-r from-red-500 to-orange-500" : "text-red-500"}
        variant={variant}
      />

      {isModal ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {app.emergency.links.map((emergencyLink, index) => (
            <div
              key={`emergency-${index}-${emergencyLink.description}`}
              className="group relative overflow-hidden rounded-2xl border border-red-500/20 backdrop-blur-sm p-6 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-red-500/20 hover:border-red-400/40"
            >
              <div className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute top-4 right-4 w-2 h-2 rounded-full animate-pulse" />

              <div className="relative z-10">
                <Button
                  asChild
                  className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                >
                  <Link
                    href={emergencyLink.link}
                    target="_blank"
                    className="flex items-center justify-center"
                  >
                    <AlertTriangle className="w-5 h-5 mr-3" />
                    <Markdown
                      content={emergencyLink.description}
                      className="flex-1 text-center [&>p]:m-0 [&>p]:text-white [&>strong]:text-white [&>em]:text-white"
                    />
                    <ExternalLink className="w-4 h-4 ml-3 opacity-70" />
                  </Link>
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {app.emergency.links.map((emergencyLink, index) => (
            <Button
              key={`emergency-${index}-${emergencyLink.description}`}
              asChild
              className="w-full justify-start h-12 bg-red-500 hover:bg-red-600 text-white"
              variant="outline"
            >
              <Link href={emergencyLink.link} target="_blank" className="flex items-center">
                <AlertTriangle className="w-4 h-4 mr-3" />
                <div className="text-left flex-1">
                  <Markdown
                    content={emergencyLink.description}
                    className="[&>p]:m-0 [&>p]:text-white [&>strong]:text-white [&>em]:text-white"
                  />
                </div>
                <ExternalLink className="w-4 h-4 ml-auto opacity-70" />
              </Link>
            </Button>
          ))}
        </div>
      )}
    </div>
  );
};
