"use client";

import React from "react";
import { Markdown } from "@/components/ui/markdown";
import { App } from "@/core.constants";
import { SectionHeader } from "./section-header";
import { Phone } from "lucide-react";

interface PhoneNumberSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

export const PhoneNumberSection = ({
  app,
  variant = "drawer",
}: PhoneNumberSectionProps) => {
  if (!app.phoneNumber.description) return null;

  const isModal = variant === "modal";

  return (
    <div>
      <SectionHeader
        icon={Phone}
        title="Phone Number"
        iconColorClass={
          isModal
            ? "bg-gradient-to-r from-orange-500 to-red-500"
            : "text-orange-500"
        }
        variant={variant}
      />
      <div
        className={
          isModal
            ? "rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
            : "rounded-lg border border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800 p-4"
        }
      >
        <Markdown
          content={app.phoneNumber.description}
          className={
            isModal
              ? "text-neutral-300"
              : "text-neutral-700 dark:text-neutral-300"
          }
        />
      </div>
    </div>
  );
};
