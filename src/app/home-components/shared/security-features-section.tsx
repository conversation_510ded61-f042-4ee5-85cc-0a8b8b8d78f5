"use client";

import React from "react";
import { App } from "@/core.constants";
import { SecurityFeature } from "../security-feature";
import { SectionHeader } from "./section-header";
import { Shield, Phone, Download } from "lucide-react";

interface SecurityFeaturesSectionProps {
  app: App;
  variant?: "modal" | "drawer";
}

type SecurityFeatureConfig = {
  key: "yubikeys" | "phoneNumber" | "backup";
  icon: React.ElementType;
  title: string;
  description: string;
  color?: string;
  getColor?: (enabled: boolean) => string;
};

const securityFeatures: SecurityFeatureConfig[] = [
  {
    key: "yubikeys",
    icon: Shield,
    title: "Yubikey Support",
    description: "Hardware security key authentication",
    color: "bg-blue-500",
  },
  {
    key: "phoneNumber",
    icon: Phone,
    title: "Phone Number Required",
    description: "Phone number dependency for 2FA",
    getColor: (enabled: boolean) => (enabled ? "bg-red-500" : "bg-green-500"),
  },
  {
    key: "backup",
    icon: Download,
    title: "Backup Codes",
    description: "Recovery codes for emergency access",
    color: "bg-purple-500",
  },
];

const ModalSecurityFeature = ({
  feature,
  app,
}: {
  feature: SecurityFeatureConfig;
  app: App;
}) => {
  const featureData = app[feature.key];
  const enabled = featureData?.enabled ?? false;
  const color = feature.getColor
    ? feature.getColor(enabled)
    : feature.color ?? "bg-gray-500";

  const getFeatureTitle = () => {
    switch (feature.key) {
      case "yubikeys":
        return "Yubikey";
      case "phoneNumber":
        return "Phone";
      case "backup":
        return "Backup";
      default:
        return feature.title;
    }
  };

  const getStatusText = () => {
    if (feature.key === "phoneNumber") {
      return enabled ? "Required" : "Optional";
    }
    return enabled ? "Supported" : "Not Available";
  };

  const getStatusColor = () => {
    if (feature.key === "phoneNumber") {
      return enabled ? "text-red-400" : "text-green-400";
    }
    return enabled ? "text-green-400" : "text-red-400";
  };

  const getButtonText = () => {
    switch (feature.key) {
      case "phoneNumber":
        return enabled ? "Remove" : "Manage";
      case "backup":
        return "Access";
      default:
        return "Setup";
    }
  };

  return (
    <div className="text-center p-3 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors">
      <div className="flex justify-center mb-2">
        <div className={`rounded-lg p-2 ${color}`}>
          <feature.icon className="w-4 h-4 text-white" />
        </div>
      </div>
      <div className="text-xs text-white font-medium">{getFeatureTitle()}</div>
      <div className={`text-xs mt-1 mb-2 ${getStatusColor()}`}>
        {getStatusText()}
      </div>
      {featureData?.link && (
        <button
          onClick={() => window.open(featureData.link, "_blank")}
          className="h-6 text-xs px-2 bg-white/10 border border-white/20 text-white hover:bg-white/20 hover:border-white/30 rounded transition-colors"
        >
          {getButtonText()}
        </button>
      )}
    </div>
  );
};

export const SecurityFeaturesSection = ({
  app,
  variant = "drawer",
}: SecurityFeaturesSectionProps) => {
  const isModal = variant === "modal";

  return (
    <div>
      <SectionHeader
        icon={Shield}
        title="Security Features"
        iconColorClass={
          isModal
            ? "bg-gradient-to-r from-blue-500 to-purple-500"
            : "text-blue-500"
        }
        variant={variant}
      />

      {isModal ? (
        <div className="grid grid-cols-3 gap-3">
          {securityFeatures.map((feature) => (
            <ModalSecurityFeature
              key={feature.key}
              feature={feature}
              app={app}
            />
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {securityFeatures.map((feature) => {
            const featureData = app[feature.key];
            const enabled = featureData?.enabled ?? false;
            const color = feature.getColor
              ? feature.getColor(enabled)
              : feature.color ?? "bg-gray-500";

            return (
              <SecurityFeature
                key={feature.key}
                icon={feature.icon}
                title={feature.title}
                enabled={enabled}
                link={featureData?.link}
                description={feature.description}
                color={color}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};
